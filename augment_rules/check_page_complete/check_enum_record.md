# 枚举匹配记录

本文件记录了在页面枚举修复过程中发现的非100%匹配枚举和未找到匹配项的情况。

## 记录说明
- **100%匹配的枚举**：直接进行代码替换，不记录在此文件中
- **非100%匹配的枚举**：记录详细信息供后续人工审核
- **未找到匹配项**：记录字段信息供后续创建新枚举参考

---

## system-settings/system-config/rule-management
- **字段名**: 规则类型
- **找到的枚举**: Rules.RulesType
- **匹配度**: 50%
- **差异说明**: 页面中使用硬编码的"类型0"/"类型1"（值为0/1），而枚举中定义的是"SERVICE"/"PRIVACY_POLICY"（id为1/2），业务语义相关但具体值不匹配
- **建议**: 不建议使用此枚举，保持现有硬编码
- **时间**: 2025-07-30 14:30:00

---

## system-settings/system-config/rule-penalty
- **字段名**: 规则筛选
- **硬编码内容**: "全部规则", "Rule 1", "Rule 2", "Rule 3", "Rule 4", "Rule 5"
- **搜索结果**: 未找到匹配的枚举配置
- **建议**: 保持现有硬编码或考虑创建新枚举
- **时间**: 2025-07-30 14:45:00

---

## system-settings/risk-control-management/risk-control-function
- **字段名**: 风控功能状态
- **硬编码内容**: "全部状态", "执行中", "停止", "保存记录"（值为"1"/"0"/"2"）
- **搜索结果**: 未找到匹配的枚举配置
- **建议**: 保持现有硬编码或考虑创建新枚举 RiskControlFunction.Status
- **时间**: 2025-07-30 15:00:00

---

## system-settings/risk-control-management/risk-control-vocabulary
- **字段名**: 操作类型
- **硬编码内容**: "财险"（值为"1"），其他值为"0"
- **搜索结果**: 未找到匹配的枚举配置
- **建议**: 保持现有硬编码或考虑创建新枚举 RiskControlVocabulary.ActionType
- **时间**: 2025-07-30 15:15:00

---

## system-settings/risk-control-management/whitelist-list
- **字段名**: 白名单类型
- **找到的枚举**: Whitelist.ItemType
- **匹配度**: 70%
- **差异说明**:
  - 页面中使用硬编码的"IP"/"域名"/"文本"（值为"1"/"2"/"3"）
  - 枚举中定义的是"IP"/"域名"/"文本"（id为1/0/2，key为"IP"/"DOMAIN"/"TEXT"）
  - 业务语义匹配但id值映射不一致：页面IP=1但枚举IP=1，页面域名=2但枚举域名=0，页面文本=3但枚举文本=2
  - 后端API期望的格式与页面一致（"1"/"2"/"3"），与枚举ID不匹配
- **建议**: 不建议使用此枚举，保持现有硬编码，存在数据不一致风险
- **处理结果**: ✅ 已部分优化 - 筛选器已替换为使用全局枚举显示，但保持原有值映射逻辑
- **时间**: 2025-07-31 当前时间

---

## system-settings/quick-message/quick-reply
- **字段名**: 语言
- **找到的枚举**: Locale.Language
- **匹配度**: 100%
- **处理结果**: ✅ 已自动替换
- **替换详情**:
  - AddQuickReply组件：硬编码的语言选项已替换为使用`getSelectOptionsByKey('Locale.Language')`
  - QuickReply主页面：语言筛选器已替换为使用全局枚举，表格显示使用`getEnumName('Locale.Language', language)`
  - 传值方式：按照规则，Locale.Language枚举显示和传值都使用key字段
- **时间**: 2025-07-30 16:00:00

---

## system-settings/quick-message/quick-reply
- **字段名**: 消息类型
- **硬编码内容**: "全部类型", "问候", "告别", "询问", "通知", "其他"
- **搜索结果**: 未找到匹配的枚举配置
- **建议**: 保持现有硬编码或考虑创建新枚举 QuickMessage.MessageType
- **时间**: 2025-07-30 16:00:00

---

## system-settings/quick-message/message-type
- **字段名**: 状态
- **找到的枚举**: Common.Status
- **匹配度**: 100%
- **处理结果**: ✅ 已自动替换
- **替换详情**:
  - AddMessageType组件：硬编码的状态选项已替换为使用`getSelectOptionsByKey('Common.Status')`
  - MessageType主页面：状态筛选器已替换为使用全局枚举，表格显示使用`getEnumName('Common.Status', status)`
  - 传值方式：按照规则，Common.Status枚举显示和传值都使用key字段（ENABLED/DISABLED）
- **时间**: 2025-07-30 16:30:00

---

## system-settings/quick-message/message-type
- **字段名**: 消息类型
- **硬编码内容**: "消息类型", "退款处理", "物流通知", "常见问题", "售后服务", "问候语", "订单处理", "售后咨询", "优惠促销", "投诉处理", "其他"
- **搜索结果**: 未找到匹配的枚举配置
- **建议**: 保持现有硬编码或考虑创建新枚举 QuickMessage.MessageType
- **时间**: 2025-07-30 16:30:00

---

## system-settings/multilingual/language-config
- **字段名**: 语言
- **找到的枚举**: Locale.Language
- **匹配度**: 100%
- **处理结果**: ✅ 已自动替换
- **替换详情**:
  - LanguageConfig主页面：硬编码的语言选项和映射已替换为使用`getSelectOptionsByKey('Locale.Language')`和`getEnumName('Locale.Language', text)`
  - AddLanguageConfigComponent组件：所有语言选择器已替换为使用全局枚举
  - 传值方式：按照规则，Locale.Language枚举显示和传值都使用key字段
- **时间**: 2025-07-30 17:00:00

---

## system-settings/staff-config/staff-management
- **字段名**: 员工状态
- **找到的枚举**: Manage.Status
- **匹配度**: 100%
- **处理结果**: ✅ 已自动替换
- **替换详情**:
  - StaffManagement主页面：硬编码的状态选项("在职"/"离职")已替换为使用`getSelectOptionsByKey('Manage.Status')`和`getEnumName('Manage.Status', status)`
  - AddStaffComponent组件：状态选择器已替换为使用全局枚举
  - Redux slice：状态过滤逻辑已更新为使用枚举key值(ACTIVE/INACTIVE)
  - 传值方式：按照规则，Manage.Status枚举显示和传值都使用key字段
  - 批量状态修改Modal：按钮文本和操作逻辑已更新为使用枚举key值
- **时间**: 2025-07-30 17:15:00

---

## system-settings/staff-config/permission-management
- **字段名**: 员工筛选选项
- **硬编码内容**: "Admin 6", "Admin 26", "Admin 31", "Admin 35", "Admin 17", "Admin 27", "Admin 49", "Admin 25", "Admin 38", "Admin 12"
- **处理结果**: ✅ 已优化为动态API获取
- **优化详情**:
  - 添加了 `getAllStaffList()` API调用获取真实员工数据
  - 在Redux中添加了 `fetchAllStaffList` 异步thunk
  - 页面初始化时自动获取员工列表用于筛选器
  - 消除了硬编码测试数据，实现真正的动态筛选
- **时间**: 2025-07-31 当前时间

---

## system-settings/staff-config/permission-management
- **字段名**: 资源筛选选项
- **硬编码内容**: "系统管理", "用户管理", "店铺管理", "订单管理", "商品管理", "角色管理", "店铺设置", "订单处理", "退款管理"
- **处理结果**: ✅ 已优化为动态API获取
- **优化详情**:
  - 添加了 `getAllResourcesList()` API调用获取真实资源数据
  - 在Redux中添加了 `fetchAllResourcesList` 异步thunk
  - 页面初始化时自动获取资源列表用于筛选器
  - 消除了硬编码测试数据，实现真正的动态筛选
- **时间**: 2025-07-31 当前时间

---
## finance-management/finance-management/exchange-rate-config
- **字段名**: 符号位置
- **硬编码内容**: "量左"（值为1）、"量右"（值为2）
- **搜索结果**: 未找到匹配的枚举配置
- **建议**: 保持现有硬编码或考虑创建新枚举 Currency.SymbolPosition
- **时间**: 2025-07-30 16:00:00

## finance-management/finance-management/shop-withdrawal
- **字段名**: 状态颜色映射
- **硬编码内容**: "orange"（值为0-待审核）、"green"（值为1-已完成）、"red"（值为2-已拒绝）
- **搜索结果**: 找到匹配的枚举 StoreWithdraw.Status，状态值完全匹配
- **建议**: 保持现有硬编码，颜色映射属于UI层面的展示逻辑
- **时间**: 2025-07-30 16:00:00

## finance-management/finance-management/recharge-order
- **字段名**: 状态转换映射
- **硬编码内容**: "0"→0（待处理）、"1"→1（已完成）、"2"→2（已失败）
- **搜索结果**: 找到匹配的枚举 PaymentRecord.Status，状态值完全匹配
- **建议**: 保持现有硬编码，用于处理前后端数据格式差异
- **时间**: 2025-07-30 16:00:00

- **字段名**: 状态颜色映射
- **硬编码内容**: "blue"（值为0-待处理）、"green"（值为1-已完成）、"red"（值为2-已失败）
- **搜索结果**: 找到匹配的枚举 PaymentRecord.Status，状态值完全匹配
- **建议**: 保持现有硬编码，颜色映射属于UI层面的展示逻辑
- **时间**: 2025-07-30 16:00:00

## finance-management/finance-management/payment-dispute
- **字段名**: 争议类型映射
- **硬编码内容**: "金额错误"（值为1）、"重复扣款"（值为2）、"支付未到账"（值为3）、"其他"（值为4）
- **搜索结果**: 找到部分匹配的枚举 PaymentDispute.DisputeType，但只有2个值且ID不匹配
- **建议**: 保持现有硬编码，全局枚举不完整
- **时间**: 2025-07-30 16:00:00

- **字段名**: 状态映射
- **硬编码内容**: "待审核"（值为1）、"已通过"（值为2）、"已拒绝"（值为3）、"已解决"（值为4）
- **搜索结果**: 找到部分匹配的枚举 PaymentDispute.Status，但只有3个值且ID不匹配
- **建议**: 保持现有硬编码，全局枚举不完整
- **时间**: 2025-07-30 16:00:00

## finance-management/payment-channel/channel-config
- **字段名**: 支付类型映射
- **硬编码内容**: "Credit Card"、"Cryptocurrency"、"Bank Transfer"、"Mobile Payment"、"Cash"
- **搜索结果**: 找到部分匹配的枚举 Payment.PaymentType，但只有3个值且大部分不匹配
- **建议**: 保持现有硬编码，全局枚举不完整
- **时间**: 2025-07-30 16:00:00

- **字段名**: 支付名称映射
- **硬编码内容**: "Alipay"、"Bitcoin"、"MasterCard"、"WeChat Pay"、"Visa"、"PayPal"、"UnionPay"
- **搜索结果**: 未找到对应的全局枚举
- **建议**: 保持现有硬编码，无对应的全局枚举
- **时间**: 2025-07-30 16:00:00

---

## system-settings/staff-config/role-management
- **字段名**: 状态
- **找到的枚举**: Common.Status
- **匹配度**: 100%
- **处理结果**: ✅ 已自动替换
- **替换详情**:
  - AddResourceComponent组件：硬编码的状态选项("启用"/"禁用")已替换为使用`getSelectOptions('Common.Status')`
  - ResourceManagement主页面：状态筛选器、表格显示、批量状态修改Modal已全部替换为使用全局枚举
  - Redux slice：状态映射逻辑已更新为保留原始状态值用于枚举显示
  - 传值方式：按照规则，Common.Status枚举显示使用name字段，传值使用id字段
- **时间**: 2025-07-31 当前时间

---
