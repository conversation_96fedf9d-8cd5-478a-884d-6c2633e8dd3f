import { useState, useCallback, useEffect, useRef } from "react";

/**
 * 通用的页面标签管理 Hook
 * @param {Object} options 配置选项
 * @param {string} options.listTabLabel 列表标签的标题，默认为 '列表'
 * @param {Object} options.tabTypes 标签类型配置对象
 * @param {Function} options.onSaveSuccess 保存成功后的回调函数
 * @param {Array} options.dataList 数据列表，用于标签切换时查找记录
 * @param {Function} options.onTabSwitch 标签切换时的回调函数
 */
export const usePageTabs = (options = {}) => {
  const {
    listTabLabel = "列表",
    tabTypes = {
      add: { label: "添加", prefix: "add" },
      edit: {
        label: "编辑",
        prefix: "edit",
        getLabelFn: (record) => `编辑 - ${record.id}`,
      },
    },
    onSaveSuccess,
    dataList = [],
    onTabSwitch,
  } = options;

  // 状态管理
  const [activeTab, setActiveTab] = useState("list");
  const [editingRecord, setEditingRecord] = useState(null);
  const [tabPanes, setTabPanes] = useState([
    {
      key: "list",
      label: listTabLabel,
      closable: false,
    },
  ]);
  // 标签访问历史记录，用于关闭标签时回到上一个标签
  const [tabHistory, setTabHistory] = useState(["list"]);
  // 保存每个标签页的表单数据状态
  const [tabFormData, setTabFormData] = useState({});

  // 保存标签页表单数据
  const saveTabFormData = useCallback((tabKey, formData) => {
    console.log(`[usePageTabs] 保存表单数据 - tabKey: ${tabKey}`, formData);
    setTabFormData((prev) => {
      const newData = {
        ...prev,
        [tabKey]: formData,
      };
      console.log(`[usePageTabs] 更新后的所有表单数据:`, newData);
      return newData;
    });
  }, []);

  // 获取标签页表单数据
  const getTabFormData = useCallback(
    (tabKey) => {
      const data = tabFormData[tabKey] || null;
      console.log(`[usePageTabs] 获取表单数据 - tabKey: ${tabKey}`, data);
      console.log(`[usePageTabs] 当前所有表单数据:`, tabFormData);
      return data;
    },
    [tabFormData]
  );

  // 清除标签页表单数据
  const clearTabFormData = useCallback((tabKey) => {
    setTabFormData((prev) => {
      const newData = { ...prev };
      delete newData[tabKey];
      return newData;
    });
  }, []);

  // 通用的创建标签函数
  const createTab = useCallback(
    (tabType, record = null, customLabel = null) => {
      const timestamp = Date.now();
      const tabConfig = tabTypes[tabType];

      if (!tabConfig) {
        console.warn(`Unknown tab type: ${tabType}`);
        return;
      }

      // 生成标签key
      let tabKey;
      if (record && record.id) {
        tabKey = `${tabConfig.prefix}-${record.id}-${timestamp}`;
      } else {
        tabKey = `${tabConfig.prefix}-${timestamp}`;
      }

      // 生成标签标题
      let tabLabel;
      if (customLabel) {
        tabLabel = customLabel;
      } else if (tabConfig.getLabelFn && record) {
        tabLabel = tabConfig.getLabelFn(record);
      } else {
        tabLabel = tabConfig.label;
      }

      // 设置编辑记录
      if (record) {
        setEditingRecord(record);
      } else {
        setEditingRecord(null);
      }

      // 创建新标签
      const newTabPanes = [
        ...tabPanes,
        {
          key: tabKey,
          label: tabLabel,
          closable: true,
          tabType: tabType,
          record: record,
        },
      ];
      setTabPanes(newTabPanes);
      setActiveTab(tabKey);
      updateTabHistory(tabKey);

      return tabKey;
    },
    [tabPanes, tabTypes]
  );

  // 添加操作处理（保持向后兼容）
  const handleAdd = useCallback(() => {
    return createTab("add");
  }, [createTab]);

  // 编辑操作处理（保持向后兼容）
  const handleEdit = useCallback(
    (record) => {
      return createTab("edit", record);
    },
    [createTab]
  );

  // 批量添加操作处理
  const handleBatchAdd = useCallback(() => {
    return createTab("batchAdd");
  }, [createTab]);

  // 更新标签历史记录
  const updateTabHistory = useCallback((newActiveTab) => {
    setTabHistory((prevHistory) => {
      // 移除历史记录中的当前标签（如果存在）
      const filteredHistory = prevHistory.filter((tab) => tab !== newActiveTab);
      // 将新标签添加到历史记录的开头
      const newHistory = [newActiveTab, ...filteredHistory];
      return newHistory;
    });
  }, []);

  // 标签切换处理
  const handleTabChange = useCallback(
    (key) => {
      setActiveTab(key);
      updateTabHistory(key);

      if (key === "list") {
        setEditingRecord(null);
      } else {
        // 查找标签配置
        const tabPane = tabPanes.find((pane) => pane.key === key);
        if (tabPane && tabPane.record) {
          setEditingRecord(tabPane.record);
        } else {
          setEditingRecord(null);
        }
      }

      // 调用外部回调
      if (onTabSwitch) {
        onTabSwitch(
          key,
          tabPanes.find((pane) => pane.key === key)
        );
      }
    },
    [tabPanes, onTabSwitch, updateTabHistory]
  );

  // 标签关闭处理
  const handleTabEdit = useCallback(
    (targetKey, action) => {
      if (action === "remove") {
        // 计算新的状态值
        const newTabPanes = tabPanes.filter((pane) => pane.key !== targetKey);
        const newHistory = tabHistory.filter((tab) => tab !== targetKey);

        // 清除对应的表单数据
        clearTabFormData(targetKey);

        // 更新标签列表和历史记录
        setTabPanes(newTabPanes);
        setTabHistory(newHistory);

        // 只有当关闭的是当前活动标签时，才需要切换到其他标签
        if (targetKey === activeTab) {
          // 查找历史记录中下一个可用的标签
          let nextTab = "list";
          for (let i = 0; i < newHistory.length; i++) {
            const historyTab = newHistory[i];
            const tabExists =
              historyTab === "list" ||
              newTabPanes.some((pane) => pane.key === historyTab);

            if (tabExists) {
              nextTab = historyTab;
              break;
            }
          }

          setActiveTab(nextTab);

          // 设置编辑记录
          if (nextTab === "list") {
            setEditingRecord(null);
          } else {
            const tabPane = newTabPanes.find((pane) => pane.key === nextTab);
            if (tabPane && tabPane.record) {
              setEditingRecord(tabPane.record);
            } else {
              setEditingRecord(null);
            }
          }
        }
      }
    },
    [activeTab, tabPanes, tabHistory]
  );

  // 移除标签
  const removeTab = useCallback(
    (targetKey) => {
      // 只移除指定的标签
      const newTabPanes = tabPanes.filter((pane) => pane.key !== targetKey);
      // 从历史记录中移除被关闭的标签
      const newHistory = tabHistory.filter((tab) => tab !== targetKey);

      // 清除对应的表单数据
      clearTabFormData(targetKey);

      // 只有当关闭的是当前活动标签时，才需要切换到其他标签
      let nextTab = activeTab;
      if (targetKey === activeTab) {
        // 查找历史记录中下一个可用的标签（从最近访问的开始）
        nextTab = "list"; // 默认回到列表标签

        for (let i = 0; i < newHistory.length; i++) {
          const historyTab = newHistory[i];
          // 检查历史记录中的标签是否仍然存在
          const tabExists =
            historyTab === "list" ||
            newTabPanes.some((pane) => pane.key === historyTab);

          if (tabExists) {
            nextTab = historyTab;
            break;
          }
        }
      }

      // 更新状态
      setTabPanes(newTabPanes);
      setTabHistory(newHistory);

      // 只有当需要切换标签时才更新 activeTab
      if (targetKey === activeTab) {
        setActiveTab(nextTab);
      }

      // 设置编辑记录
      if (nextTab === "list") {
        setEditingRecord(null);
      } else {
        const tabPane = newTabPanes.find((pane) => pane.key === nextTab);
        if (tabPane && tabPane.record) {
          setEditingRecord(tabPane.record);
        } else {
          setEditingRecord(null);
        }
      }
    },
    [tabPanes, activeTab, tabHistory]
  );

  // 保存成功处理
  const handleSaveSuccess = useCallback(() => {
    // 移除当前的添加/编辑标签
    if (activeTab !== "list") {
      removeTab(activeTab);
    }
    setActiveTab("list");
    setEditingRecord(null);

    // 调用外部回调
    if (onSaveSuccess) {
      onSaveSuccess();
    }
  }, [activeTab, removeTab, onSaveSuccess]);

  // 取消处理
  const handleCancel = useCallback(() => {
    // 移除当前的添加/编辑标签
    if (activeTab !== "list") {
      removeTab(activeTab);
    }
    setActiveTab("list");
    setEditingRecord(null);
  }, [activeTab, removeTab]);

  // 获取当前标签信息
  const getCurrentTab = useCallback(() => {
    if (activeTab === "list") {
      return { type: "list", isListTab: true };
    }
    const tabPane = tabPanes.find((pane) => pane.key === activeTab);
    return {
      type: tabPane?.tabType || "unknown",
      isListTab: false,
      tabPane: tabPane,
      record: tabPane?.record,
    };
  }, [activeTab, tabPanes]);

  // 判断标签类型的工具函数
  const isTabType = useCallback(
    (type) => {
      const currentTab = getCurrentTab();
      return currentTab.type === type;
    },
    [getCurrentTab]
  );

  // 向后兼容的判断函数
  const isAddTab = isTabType("add");
  const isEditTab = isTabType("edit");
  const isListTab = activeTab === "list";

  // 获取标签历史记录（调试用）
  const getTabHistory = useCallback(() => {
    return [...tabHistory];
  }, [tabHistory]);

  // 清空标签历史记录（高级用法）
  const clearTabHistory = useCallback(() => {
    setTabHistory(["list"]);
  }, []);

  return {
    // 状态
    activeTab,
    editingRecord,
    tabPanes,
    tabHistory, // 标签历史记录

    // 核心函数
    createTab, // 通用创建标签函数
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    removeTab,

    // 向后兼容的处理函数
    handleAdd,
    handleEdit,
    handleBatchAdd,

    // 工具函数
    getCurrentTab, // 获取当前标签信息
    isTabType, // 判断标签类型
    isAddTab, // 向后兼容
    isEditTab, // 向后兼容
    isListTab,
    getTabHistory, // 获取标签历史记录
    clearTabHistory, // 清空标签历史记录

    // 表单数据管理函数
    saveTabFormData, // 保存标签页表单数据
    getTabFormData, // 获取标签页表单数据
    clearTabFormData, // 清除标签页表单数据

    // 直接设置状态的函数（高级用法）
    setActiveTab,
    setEditingRecord,
    setTabPanes,
  };
};
