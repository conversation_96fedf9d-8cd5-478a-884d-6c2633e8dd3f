import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Space,
  Row,
  Col,
  message,
  Table,
  Popconfirm,
  Typography
} from 'antd';
import { PlusOutlined, DeleteOutlined, SaveOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { batchCreateQuickMessage } from '../../../redux/quickMessagePage/quickMessagePageSlice';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';

const { Option } = Select;
const { TextArea } = Input;
const { Title } = Typography;

const BatchAddQuickReply = ({ onSave, onCancel, tabKey, saveTabFormData, getTabFormData, clearTabFormData }) => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [submitting, setSubmitting] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [editingKey, setEditingKey] = useState('');
  const { getSelectOptionsByKey } = useGlobalConstants();

  // 获取语言选项（Locale.Language枚举显示和传值都使用key字段）
  const languageOptions = getSelectOptionsByKey('Locale.Language') || [];

  // 消息类型选项（临时硬编码，后续可以从枚举获取）
  const messageTypeOptions = [
    { label: '问候', value: '问候' },
    { label: '告别', value: '告别' },
    { label: '询问', value: '询问' },
    { label: '通知', value: '通知' },
    { label: '其他', value: '其他' }
  ];

  // 初始化时添加一行空数据
  useEffect(() => {
    // 尝试从标签页恢复数据
    if (tabKey && getTabFormData) {
      const savedData = getTabFormData(tabKey);
      if (savedData && savedData.length > 0) {
        setDataSource(savedData);
        return;
      }
    }

    // 如果没有保存的数据，添加一行空数据
    if (dataSource.length === 0) {
      addNewRow();
    }
  }, [tabKey, getTabFormData]);

  // 保存数据到标签页
  const saveDataToTab = (data) => {
    if (tabKey && saveTabFormData) {
      saveTabFormData(tabKey, data);
    }
  };

  // 添加新行
  const addNewRow = () => {
    const newKey = Date.now().toString();
    const newRow = {
      key: newKey,
      messageType: '',
      language: '',
      content: '',
      description: ''
    };
    const newData = [...dataSource, newRow];
    setDataSource(newData);
    saveDataToTab(newData);
    setEditingKey(newKey);
  };

  // 删除行
  const deleteRow = (key) => {
    const newData = dataSource.filter(item => item.key !== key);
    setDataSource(newData);
    saveDataToTab(newData);
    if (editingKey === key) {
      setEditingKey('');
    }
  };

  // 编辑行
  const edit = (record) => {
    form.setFieldsValue({
      messageType: record.messageType,
      language: record.language,
      content: record.content,
      description: record.description,
    });
    setEditingKey(record.key);
  };

  // 取消编辑
  const cancel = () => {
    setEditingKey('');
    form.resetFields();
  };

  // 保存行
  const save = async (key) => {
    try {
      const row = await form.validateFields();
      const newData = [...dataSource];
      const index = newData.findIndex(item => key === item.key);

      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, { ...item, ...row });
        setDataSource(newData);
        saveDataToTab(newData);
        setEditingKey('');
        form.resetFields();
      }
    } catch (errInfo) {
      console.log('验证失败:', errInfo);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '消息类型',
      dataIndex: 'messageType',
      width: '20%',
      render: (text, record) => {
        if (editingKey === record.key) {
          return (
            <Form.Item
              name="messageType"
              style={{ margin: 0 }}
              rules={[{ required: true, message: '请选择消息类型' }]}
            >
              <Select placeholder="选择消息类型">
                {messageTypeOptions.map(type => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          );
        }
        return messageTypeOptions.find(type => type.value === text)?.label || text;
      },
    },
    {
      title: '语言',
      dataIndex: 'language',
      width: '15%',
      render: (text, record) => {
        if (editingKey === record.key) {
          return (
            <Form.Item
              name="language"
              style={{ margin: 0 }}
              rules={[{ required: true, message: '请选择语言' }]}
            >
              <Select placeholder="选择语言">
                {languageOptions.map(lang => (
                  <Option key={lang.value} value={lang.value}>
                    {lang.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          );
        }
        return languageOptions.find(lang => lang.value === text)?.label || text;
      },
    },
    {
      title: '回复内容',
      dataIndex: 'content',
      width: '35%',
      render: (text, record) => {
        if (editingKey === record.key) {
          return (
            <Form.Item
              name="content"
              style={{ margin: 0 }}
              rules={[{ required: true, message: '请输入回复内容' }]}
            >
              <TextArea
                placeholder="请输入回复内容"
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </Form.Item>
          );
        }
        return (
          <div style={{
            maxHeight: '60px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'pre-wrap'
          }}>
            {text}
          </div>
        );
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: '20%',
      render: (text, record) => {
        if (editingKey === record.key) {
          return (
            <Form.Item
              name="description"
              style={{ margin: 0 }}
            >
              <Input placeholder="请输入描述" />
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '操作',
      width: '10%',
      render: (_, record) => {
        const editable = editingKey === record.key;
        if (editable) {
          return (
            <Space>
              <Button
                type="link"
                size="small"
                onClick={() => save(record.key)}
                style={{ padding: 0 }}
              >
                保存
              </Button>
              <Button
                type="link"
                size="small"
                onClick={cancel}
                style={{ padding: 0 }}
              >
                取消
              </Button>
            </Space>
          );
        } else {
          return (
            <Space>
              <Button
                type="link"
                size="small"
                onClick={() => edit(record)}
                style={{ padding: 0 }}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定删除这行数据吗？"
                onConfirm={() => deleteRow(record.key)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  style={{ padding: 0 }}
                >
                  删除
                </Button>
              </Popconfirm>
            </Space>
          );
        }
      },
    },
  ];

  // 提交批量添加
  const handleSubmit = async () => {
    // 检查是否有正在编辑的行
    if (editingKey) {
      message.warning('请先保存正在编辑的行');
      return;
    }

    // 检查是否有数据
    if (dataSource.length === 0) {
      message.warning('请至少添加一条数据');
      return;
    }

    // 验证所有行的必填字段
    const invalidRows = dataSource.filter(row =>
      !row.messageType || !row.language || !row.content
    );

    if (invalidRows.length > 0) {
      message.warning('请完善所有行的必填信息（消息类型、语言、回复内容）');
      return;
    }

    try {
      setSubmitting(true);

      // 准备提交数据
      const submitData = dataSource.map(row => ({
        messageType: row.messageType,
        language: row.language,
        content: row.content,
        description: row.description || ''
      }));

      await dispatch(batchCreateQuickMessage(submitData)).unwrap();

      // 清除标签页数据
      if (tabKey && clearTabFormData) {
        clearTabFormData(tabKey);
      }

      // 重置数据
      setDataSource([]);
      setEditingKey('');

      onSave && onSave();
    } catch (error) {
      console.error('批量添加失败:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div style={{ padding: '16px' }}>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={4}>批量添加快速回复</Title>
          <p style={{ color: '#666', marginBottom: 16 }}>
            您可以批量添加多条快速回复。点击"添加行"增加新的回复，点击"编辑"修改内容，完成后点击"批量提交"保存所有数据。
          </p>
        </div>

        <Form form={form} component={false}>
          <Table
            components={{
              body: {
                cell: ({ children, ...restProps }) => (
                  <td {...restProps}>{children}</td>
                ),
              },
            }}
            bordered
            dataSource={dataSource}
            columns={columns}
            rowClassName="editable-row"
            pagination={false}
            scroll={{ x: 800 }}
            size="middle"
          />
        </Form>

        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Space>
            <Button
              type="dashed"
              onClick={addNewRow}
              icon={<PlusOutlined />}
              style={{ marginBottom: 16 }}
            >
              添加行
            </Button>
          </Space>
        </div>

        <div style={{ marginTop: 24, textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>
              取消
            </Button>
            <Button
              type="primary"
              loading={submitting}
              onClick={handleSubmit}
              icon={<SaveOutlined />}
              style={{
                backgroundColor: "#ff7a00",
                borderColor: "#ff7a00",
              }}
            >
              批量提交 ({dataSource.length} 条)
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default BatchAddQuickReply;
