import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  Input,
  Select,
  Space,
  message,
  Row,
  Col,
  Card,
  Typography,
  Popconfirm,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchQuickMessageList,
  removeQuickMessage,
  batchRemoveQuickMessage,
  setSearchParams,
  setSelectedRowKeys,
  setPagination,
} from "../../../redux/quickMessagePage/quickMessagePageSlice";
import PageTabs from '@/components/PageTabs/PageTabs';
import AddQuickReply from './AddQuickReply';
import BatchAddQuickReply from './BatchAddQuickReply';
import { usePageTabs } from '@/hooks/usePageTabs';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';
import { PlusOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';

const { Option } = Select;
const { Title } = Typography;

const QuickReplyPage = () => {
  const dispatch = useDispatch();

  // 从Redux获取状态
  const { list, searchParams, selectedRowKeys, deleteData, batchDeleteData } = useSelector(
    (state) => state.quickMessage
  );
  const loading = list.loading;

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  // 获取全局枚举配置
  const { getSelectOptionsByKey, getEnumName } = useGlobalConstants();

  // 本地状态
  const [searchText, setSearchText] = useState("");
  const [languageFilter, setLanguageFilter] = useState("全部语言");
  const [typeFilter, setTypeFilter] = useState("全部类型");

  // 获取语言选项（Locale.Language枚举显示和传值都使用key字段）
  const languageOptions = [
    { label: "全部语言", value: "全部语言" },
    ...getSelectOptionsByKey('Locale.Language')
  ];

  // 使用通用的标签管理 Hook
  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleBatchAdd: handleBatchAddTab,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: '快速回复',
    tabTypes: {
      add: {
        label: '添加快速回复',
        prefix: 'add'
      },
      edit: {
        label: '编辑',
        prefix: 'edit',
        getLabelFn: (record) => `编辑快速回复 - ${record.id}`
      },
      batchAdd: {
        label: '批量添加快速回复',
        prefix: 'batch-add'
      }
    },
    dataList: list.data,
    onSaveSuccess: () => {
      dispatch(fetchQuickMessageList()); // 重新获取数据
    },
  });

  // 首次加载时获取数据
  useEffect(() => {
    dispatch(fetchQuickMessageList());
  }, [dispatch]);

  // 表格列配置
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: "快捷消息类型ID",
      dataIndex: "templateId",
      key: "templateId",
      width: 140,
    },
    {
      title: "消息类型",
      dataIndex: "platform",
      key: "platform",
      width: 120,
    },
    {
      title: "语言",
      dataIndex: "language",
      key: "language",
      width: 120,
      render: (language) => {
        // 使用全局枚举显示语言名称
        return getEnumName('Locale.Language', language) || language;
      },
    },
    {
      title: "消息内容",
      dataIndex: "content",
      key: "content",
      width: 300,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 180,
    },
    {
      title: "更新时间",
      dataIndex: "updateTime",
      key: "updateTime",
      width: 180,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除该条记录吗?"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys) => {
      dispatch(setSelectedRowKeys(newSelectedRowKeys));
    },
  };

  // 批量操作处理
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的记录");
      return;
    }
    dispatch(batchRemoveQuickMessage(selectedRowKeys));
  };

  const handleBatchAdd = () => {
    // 打开批量添加标签页
    handleBatchAddTab();
  };

  // 删除处理函数

  const handleDelete = (record) => {
    dispatch(removeQuickMessage(record.id));
  };

  const handleSearch = () => {
    // 构建搜索参数
    const params = {
      messageType: typeFilter === "全部类型" ? "" : typeFilter,
      language: languageFilter === "全部语言" ? "" : languageFilter,
      content: searchText,
      // 重置分页到第一页
      page: 1
    };

    // 更新 Redux 中的搜索参数
    dispatch(setSearchParams(params));

    // 重置分页状态并获取数据
    dispatch(setPagination({
      current: 1,
      pageSize: list.pagination.pageSize
    }));

    // 获取数据
    dispatch(fetchQuickMessageList());
  };


  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    // 更新分页信息
    dispatch(setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
    }));

    // 获取数据，保留当前的搜索条件
    dispatch(fetchQuickMessageList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      // 如果有排序，也可以添加排序参数
      ...(sorter.field && sorter.order ? {
        sortField: sorter.field,
        sortOrder: sorter.order
      } : {})
    }));
  };

  return (
    <Card style={{ backgroundColor: '#fff' }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 根据当前标签显示不同内容 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Select
                  value={languageFilter}
                  onChange={setLanguageFilter}
                  style={{ width: 120 }}
                  options={languageOptions}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Col>
              <Col>
                <Select
                  value={typeFilter}
                  onChange={setTypeFilter}
                  style={{ width: 120 }}
                >
                  <Option value="全部类型">全部类型</Option>
                  <Option value="问候">问候</Option>
                  <Option value="告别">告别</Option>
                  <Option value="询问">询问</Option>
                  <Option value="通知">通知</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Col>
              <Col>
                <Input
                  placeholder="消息内容"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 200 }}
                  prefix={<SearchOutlined />}
                />
              </Col>
              <Col>
                <Button
                  type="primary"
                  onClick={handleSearch}
                  loading={loading}
                  icon={<SearchOutlined />}
                >
                  查询
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
                icon={<PlusOutlined />}
              >
                添加
              </Button>
              <Popconfirm
                title="确定批量删除"
                description={`确定要删除选中的 ${selectedRowKeys.length} 个快速回复吗？`}
                onConfirm={handleBatchDelete}
                disabled={selectedRowKeys.length === 0}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  danger
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                  icon={<DeleteOutlined />}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                onClick={handleBatchAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量添加
              </Button>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              rowSelection={rowSelection}
              columns={columns}
              dataSource={list.data}
              loading={loading || isDeleting}
              rowKey="id"
              pagination={{
                current: list.pagination.current,
                pageSize: list.pagination.pageSize,
                total: list.pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `总计 ${total} 条数据`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              onChange={handleTableChange}
              scroll={{ x: 1200 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : activeTab.startsWith('batch-add-') ? (
        /* 批量添加标签内容 */
        <BatchAddQuickReply
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      ) : (
        /* 添加/编辑标签内容 */
        <AddQuickReply
          editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}
    </Card>
  );
};

export default QuickReplyPage;
