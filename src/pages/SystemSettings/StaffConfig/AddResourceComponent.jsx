import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  message,
  Select,
} from "antd";
import { SaveOutlined, UndoOutlined } from "@ant-design/icons";
import { useGlobalConstants } from '@/hooks/useGlobalConstants';
import {
  addResource,
  updateResource,
} from "@/redux/resourceManagementPage/resourceManagementPageSlice";

const { Title } = Typography;
const { Option } = Select;

function AddResourceComponent({ editingRecord, onSave, onCancel }) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 获取全局枚举配置
  const { getSelectOptions, getEnumName } = useGlobalConstants();

  // 获取状态选项（使用 id 作为 value）
  const statusOptions = getSelectOptions('Common.Status');

  // 层级值转换
  const getLevelValue = (level) => {
    switch (level) {
      case "菜单":
        return "1";
      case "模块":
        return "2";
      case "页面":
        return "3";
      case "标签":
        return "4";
      default:
        return undefined;
    }
  };

  useEffect(() => {
    if (editingRecord) {
      form.setFieldsValue({
        parentMenuName: editingRecord.父名称,
        level: getLevelValue(editingRecord.层级),
        status: editingRecord.状态 === "启用" ? 1 : 0,
        menuName: editingRecord.菜单名称,
        menuPath: editingRecord.菜单路径,
        resourcesKey: editingRecord.资源键,
      });
    } else {
      form.resetFields();
    }
  }, [editingRecord, form]);

  const handleFormSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      if (editingRecord) {
        await dispatch(
          updateResource({
            id: editingRecord.id,
            resourceData: values,
          })
        ).unwrap();
        message.success("更新成功");
      } else {
        await dispatch(addResource(values)).unwrap();
        message.success("添加成功");
        form.resetFields();
      }

      onSave && onSave();
    } catch (error) {
      console.error("操作失败:", error);
      message.error(editingRecord ? "更新失败" : "添加失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel && onCancel();
  };

  return (
    <div>
      <Title level={3}>{editingRecord ? "编辑资源" : "添加资源"}</Title>
      <Card>
        <Form form={form} layout="vertical" style={{ maxWidth: 600 }}>
          <Form.Item
            name="parentMenuName"
            label="父菜单名称"
            rules={[
              { required: true, message: "请输入父菜单名称" },
              { max: 32, message: "父菜单名称最多32个字符" },
            ]}
          >
            <Input placeholder="请输入父菜单名称" />
          </Form.Item>
          <Form.Item
            name="level"
            label="层级"
            rules={[{ required: true, message: "请选择层级" }]}
          >
            <Select placeholder="请选择层级">
              <Option value="1">菜单</Option>
              <Option value="2">模块</Option>
              <Option value="3">页面</Option>
              <Option value="4">标签</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select
              placeholder="请选择状态"
              options={statusOptions}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
          <Form.Item
            name="menuName"
            label="菜单名称"
            rules={[
              { required: true, message: "请输入菜单名称" },
              { max: 32, message: "菜单名称最多32个字符" },
            ]}
          >
            <Input placeholder="请输入菜单名称" />
          </Form.Item>
          <Form.Item
            name="menuPath"
            label="菜单路径"
            rules={[
              { required: true, message: "请输入菜单路径" },
              { max: 128, message: "菜单路径最多128个字符" },
            ]}
          >
            <Input placeholder="请输入菜单路径" />
          </Form.Item>
          <Form.Item
            name="resourcesKey"
            label="资源键"
            rules={[
              { required: true, message: "请输入资源键" },
              { pattern: /^[0-9-]+$/, message: "资源键只能包含数字和连字符" },
              { max: 32, message: "资源键最多32个字符" },
            ]}
          >
            <Input placeholder="请输入资源键，例如：2-9-1-1" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                onClick={handleFormSubmit}
                icon={<SaveOutlined />}
                loading={loading}
              >
                保存
              </Button>
              <Button
                onClick={handleCancel}
                icon={<UndoOutlined />}
                disabled={loading}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}

export default AddResourceComponent;
