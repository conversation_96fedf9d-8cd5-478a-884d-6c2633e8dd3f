import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Select,
  Popconfirm,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import {
  fetchResourcesPermissions,
  deleteResourcesPermission,
  batchDeleteResourcesPermissions,
  addResourcesPermission,
  updateResourcesPermission,
  setFilters,
  setPagination,
  resetResourcePermissionState,
} from "@/redux/resourcePermissionPage/resourcePermissionPageSlice";
import PageTabs from "@/components/PageTabs/PageTabs";
import AddResourcePermissionComponent from "./AddResourcePermissionComponent";
import { usePageTabs } from "@/hooks/usePageTabs";

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

function ResourcePermissionPage() {
  const dispatch = useDispatch();

  // 从Redux获取状态
  const {
    permissionList,
    loading,
    pagination,
    filters,
    deleteData,
    batchDeleteData,
  } = useSelector((state) => state.resourcePermission);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  // 使用PageTabs hook
  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: "资源权限管理",
    tabTypes: {
      add: {
        label: "添加资源权限",
        prefix: "add",
      },
      edit: {
        label: "编辑",
        prefix: "edit",
        getLabelFn: (record) => `编辑资源权限 - ${record.权限名称}`,
      },
    },
    dataList: permissionList,
    onSaveSuccess: () => {
      fetchPermissionsList();
    },
  });

  // 初始加载数据
  useEffect(() => {
    fetchPermissionsList();

    // 组件卸载时重置状态
    return () => {
      dispatch(resetResourcePermissionState());
    };
  }, [dispatch]);

  // 获取资源权限列表
  const fetchPermissionsList = () => {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      permissionName: filters.permissionName || undefined,
      startDate: filters.dateRange
        ? dayjs(filters.dateRange).format("YYYY-MM-DD")
        : undefined,
    };

    dispatch(fetchResourcesPermissions(params));
  };

  // 获取Tag颜色
  const getFieldColor = () => "red";
  const getButtonColor = () => "blue";
  const getUrlColor = () => "green";

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "资源名称",
      dataIndex: "资源名称",
      key: "资源名称",
      width: 120,
    },
    {
      title: "权限名称",
      dataIndex: "权限名称",
      key: "权限名称",
      width: 120,
    },
    {
      title: "字段",
      dataIndex: "字段",
      key: "字段",
      width: 200,
      render: (fields) => (
        <Space wrap>
          {fields.map((field, index) => (
            <Tag key={field} color={getFieldColor(index)}>
              {field}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: "按钮",
      dataIndex: "按钮",
      key: "按钮",
      width: 200,
      render: (buttons) => (
        <Space wrap>
          {buttons.map((button, index) => (
            <Tag key={button} color={getButtonColor(index)}>
              {button}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: "URL",
      dataIndex: "URL",
      key: "URL",
      width: 200,
      render: (urls) => (
        <Space wrap>
          {urls.map((url, index) => (
            <Tag key={url} color={getUrlColor(index)}>
              {url}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "创建时间",
      key: "创建时间",
      width: 180,
      sorter: (a, b) => new Date(a.创建时间) - new Date(b.创建时间),
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除"
            description={`确定要删除权限 "${record.权限名称}" 吗？`}
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的资源权限");
      return;
    }

    try {
      // 将字符串key转换为数字id
      const ids = selectedRowKeys.map((key) => parseInt(key));
      await dispatch(batchDeleteResourcesPermissions(ids)).unwrap();
      message.success(`已删除 ${selectedRowKeys.length} 个资源权限`);
      setSelectedRowKeys([]);
      fetchPermissionsList();
    } catch (error) {
      message.error("批量删除失败");
    }
  };

  // 处理删除
  const handleDelete = async (record) => {
    try {
      await dispatch(deleteResourcesPermission(record.id)).unwrap();
      message.success(`已删除资源权限 ID: ${record.id}`);
      fetchPermissionsList();
    } catch (error) {
      message.error("删除失败");
    }
  };

  // 处理查询
  const handleSearch = () => {
    dispatch(setPagination({ current: 1 })); // 重置到第一页
    fetchPermissionsList();
  };

  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    dispatch(
      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
      })
    );

    // 直接使用新的pagination参数获取数据
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      permissionName: filters.permissionName || undefined,
      startDate: filters.dateRange
        ? dayjs(filters.dateRange).format("YYYY-MM-DD")
        : undefined,
    };

    dispatch(fetchResourcesPermissions(params));
  };

  return (
    <Card style={{ backgroundColor: "#fff" }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Input
                  placeholder="搜索权限名称"
                  value={filters.permissionName}
                  onChange={(e) =>
                    dispatch(setFilters({ permissionName: e.target.value }))
                  }
                  style={{ width: 150 }}
                  prefix={<SearchOutlined />}
                />
              </Col>
              <Col>
                <span>年/月/日</span>
              </Col>
              <Col>
                <DatePicker
                  placeholder="年/月/日"
                  style={{ width: 120 }}
                  onChange={(date) => dispatch(setFilters({ dateRange: date }))}
                />
              </Col>
              <Col>
                <Button
                  type="primary"
                  onClick={handleSearch}
                  icon={<SearchOutlined />}
                >
                  查询
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
                icon={<PlusOutlined />}
              >
                添加
              </Button>
              <Popconfirm
                title="确定批量删除"
                description={`确定要删除选中的 ${selectedRowKeys.length} 个资源权限吗？`}
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
                disabled={selectedRowKeys.length === 0}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={permissionList}
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                pageSizeOptions: ["10", "20", "50"],
              }}
              onChange={handleTableChange}
              rowKey="key"
              scroll={{ x: 1400 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : (
        <AddResourcePermissionComponent
          editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}
    </Card>
  );
}

export default ResourcePermissionPage;
