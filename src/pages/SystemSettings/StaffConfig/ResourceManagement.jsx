import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  Input,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Popconfirm,
  Modal,
  Alert,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useGlobalConstants } from '@/hooks/useGlobalConstants';
import {
  fetchResources,
  deleteResource,
  batchDeleteResources,
  addResource,
  updateResource,
  batchUpdateResourcesStatus,
  setFilters,
  setPagination,
  resetResourceManagementState,
} from "@/redux/resourceManagementPage/resourceManagementPageSlice";
import PageTabs from "@/components/PageTabs/PageTabs";
import AddResourceComponent from "./AddResourceComponent";
import { usePageTabs } from "@/hooks/usePageTabs";

const { Title } = Typography;
const { Option } = Select;

function ResourceManagementPage() {
  const dispatch = useDispatch();

  // 从Redux获取状态
  const { resourceList, loading, pagination, filters } = useSelector(
    (state) => state.resourceManagement
  );

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 批量状态修改Modal相关状态
  const [batchStatusModalVisible, setBatchStatusModalVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(1); // 默认启用

  // 获取全局枚举配置
  const { getSelectOptions, getEnumName } = useGlobalConstants();

  // 获取状态选项（使用 id 作为 value）
  const statusOptions = [
    { label: "全部状态", value: "全部状态" },
    ...getSelectOptions('Common.Status')
  ];

  // PageTabs状态管理
  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
  } = usePageTabs({
    listTabLabel: "资源管理",
    tabTypes: {
      add: {
        label: "添加资源",
        prefix: "add",
      },
      edit: {
        label: "编辑",
        prefix: "edit",
        getLabelFn: (record) => `编辑资源 - ${record.菜单名称}`,
      },
    },
    dataList: resourceList,
    onSaveSuccess: () => {
      fetchResourcesList();
    },
  });

  // 初始加载数据
  useEffect(() => {
    fetchResourcesList();

    // 组件卸载时重置状态
    return () => {
      dispatch(resetResourceManagementState());
    };
  }, [dispatch]);

  // 获取资源列表
  const fetchResourcesList = () => {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      status:
        filters.status !== "全部状态"
          ? filters.status
          : undefined,
      level:
        filters.level !== "全部层级" ? getLevelValue(filters.level) : undefined,
      menuName: filters.menuName || undefined,
      startDate: filters.dateRange
        ? dayjs(filters.dateRange).format("YYYY-MM-DD")
        : undefined,
    };

    dispatch(fetchResources(params));
  };

  // 层级值转换
  const getLevelValue = (level) => {
    switch (level) {
      case "菜单":
        return "1";
      case "模块":
        return "2";
      case "页面":
        return "3";
      case "标签":
        return "4";
      default:
        return undefined;
    }
  };

  // 获取状态标签颜色
  const getStatusColor = (status) => {
    const statusName = getEnumName('Common.Status', status);
    switch (statusName) {
      case "启用":
        return "green";
      case "禁用":
        return "red";
      default:
        return "default";
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "父名称",
      dataIndex: "父名称",
      key: "父名称",
      width: 100,
    },
    {
      title: "层级",
      dataIndex: "层级",
      key: "层级",
      width: 80,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 80,
      render: (status) => {
        const statusName = getEnumName('Common.Status', status) || '未知';
        return <Tag color={getStatusColor(status)}>{statusName}</Tag>;
      },
    },
    {
      title: "菜单名称",
      dataIndex: "菜单名称",
      key: "菜单名称",
      width: 120,
    },
    {
      title: "菜单路径",
      dataIndex: "菜单路径",
      key: "菜单路径",
      width: 150,
      ellipsis: true,
    },
    {
      title: "资源键",
      dataIndex: "资源键",
      key: "资源键",
      width: 120,
    },
    {
      title: "创建时间",
      dataIndex: "创建时间",
      key: "创建时间",
      width: 180,
      sorter: (a, b) => new Date(a.创建时间) - new Date(b.创建时间),
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除"
            description={`确定要删除资源 "${record.菜单名称}" 吗？`}
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的资源");
      return;
    }

    try {
      // 将字符串key转换为数字id
      const ids = selectedRowKeys.map((key) => parseInt(key));
      await dispatch(batchDeleteResources(ids)).unwrap();
      message.success(`已删除 ${selectedRowKeys.length} 个资源`);
      setSelectedRowKeys([]);
      fetchResourcesList();
    } catch (error) {
      message.error("批量删除失败");
    }
  };

  // 处理批量修改状态 - 触发函数
  const handleBatchModifyStatus = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改状态的资源");
      return;
    }
    setBatchStatusModalVisible(true);
    setSelectedStatus(1); // 重置为默认值
  };

  // 处理批量修改状态 - 确认函数
  const handleBatchStatusConfirm = async () => {
    try {
      // 将字符串key转换为数字id
      const ids = selectedRowKeys.map((key) => parseInt(key));
      await dispatch(batchUpdateResourcesStatus({ ids, status: selectedStatus.toString() })).unwrap();
      const statusName = getEnumName('Common.Status', selectedStatus) || '未知';
      message.success(
        `已修改 ${selectedRowKeys.length} 个资源的状态为 ${statusName}`
      );
      setSelectedRowKeys([]);
      fetchResourcesList();
      setBatchStatusModalVisible(false);
    } catch (error) {
      message.error("批量修改状态失败");
    }
  };

  // 处理批量修改状态 - 取消函数
  const handleBatchStatusCancel = () => {
    setBatchStatusModalVisible(false);
    setSelectedStatus(1);
  };

  // 处理删除
  const handleDelete = async (record) => {
    try {
      await dispatch(deleteResource(record.id)).unwrap();
      message.success(`已删除资源 ID: ${record.id}`);
      fetchResourcesList();
    } catch (error) {
      message.error("删除失败");
    }
  };

  // 处理查询
  const handleSearch = () => {
    dispatch(setPagination({ current: 1 })); // 重置到第一页
    fetchResourcesList();
  };

  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    dispatch(
      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
      })
    );

    // 直接使用新的pagination参数获取数据
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      status:
        filters.status !== "全部状态"
          ? filters.status
          : undefined,
      level:
        filters.level !== "全部层级" ? getLevelValue(filters.level) : undefined,
      menuName: filters.menuName || undefined,
      startDate: filters.dateRange
        ? dayjs(filters.dateRange).format("YYYY-MM-DD")
        : undefined,
    };

    dispatch(fetchResources(params));
  };

  return (
    <>
      <Card style={{ backgroundColor: "#fff" }}>
        {/* 页面标签栏 */}
        <div className="page-tabs-wrapper">
          <PageTabs
            activeKey={activeTab}
            onChange={handleTabChange}
            onEdit={handleTabEdit}
            items={tabPanes}
            type="editable-card"
          />
        </div>

        {/* 条件渲染 */}
        {isListTab ? (
          <>
            {/* 筛选条件 */}
            <Card style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col>
                  <Select
                    value={filters.status}
                    onChange={(value) => dispatch(setFilters({ status: value }))}
                    style={{ width: 120 }}
                    options={statusOptions}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </Col>
                <Col>
                  <Select
                    value={filters.level}
                    onChange={(value) => dispatch(setFilters({ level: value }))}
                    style={{ width: 120 }}
                  >
                    <Option value="全部层级">全部层级</Option>
                    <Option value="菜单">菜单</Option>
                    <Option value="模块">模块</Option>
                    <Option value="页面">页面</Option>
                    <Option value="标签">标签</Option>
                  </Select>
                </Col>
                <Col>
                  <Input
                    placeholder="搜索菜单名称"
                    value={filters.menuName}
                    onChange={(e) =>
                      dispatch(setFilters({ menuName: e.target.value }))
                    }
                    style={{ width: 150 }}
                    prefix={<SearchOutlined />}
                  />
                </Col>
                <Col>
                  <span>年/月/日</span>
                </Col>
                <Col>
                  <DatePicker
                    placeholder="年/月/日"
                    style={{ width: 120 }}
                    onChange={(date) => dispatch(setFilters({ dateRange: date }))}
                  />
                </Col>
                <Col>
                  <Button
                    type="primary"
                    onClick={handleSearch}
                    icon={<SearchOutlined />}
                  >
                    查询
                  </Button>
                </Col>
              </Row>
            </Card>

            {/* 批量操作按钮 */}
            <Card style={{ marginBottom: 16 }}>
              <Space wrap>
                <Button
                  onClick={handleAdd}
                  style={{
                    backgroundColor: "#ff7a00",
                    borderColor: "#ff7a00",
                    color: "white",
                  }}
                  icon={<PlusOutlined />}
                >
                  添加
                </Button>
                <Popconfirm
                  title="确定批量删除"
                  description={`确定要删除选中的 ${selectedRowKeys.length} 个资源吗？`}
                  onConfirm={handleBatchDelete}
                  okText="确定"
                  cancelText="取消"
                  disabled={selectedRowKeys.length === 0}
                >
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    disabled={selectedRowKeys.length === 0}
                  >
                    批量删除
                  </Button>
                </Popconfirm>
                <Button
                  onClick={handleBatchModifyStatus}
                  disabled={selectedRowKeys.length === 0}
                  style={{
                    backgroundColor: "#ff9500",
                    borderColor: "#ff9500",
                    color: "white",
                  }}
                >
                  批量修改状态
                </Button>
              </Space>
            </Card>

            {/* 数据表格 */}
            <Card>
              <Table
                columns={columns}
                dataSource={resourceList}
                loading={loading}
                rowSelection={rowSelection}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`,
                  pageSizeOptions: ["10", "20", "50"],
                }}
                onChange={handleTableChange}
                rowKey="key"
                scroll={{ x: 1400 }}
                size="middle"
                bordered
              />
            </Card>
          </>
        ) : (
          <AddResourceComponent
            editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
            onSave={handleSaveSuccess}
            onCancel={handleCancel}
          />
        )}
      </Card>

      {/* 批量状态修改Modal */}
      <Modal
        title="批量修改资源状态"
        open={batchStatusModalVisible}
        onOk={handleBatchStatusConfirm}
        onCancel={handleBatchStatusCancel}
        okText="确定修改"
        cancelText="取消"
        width={500}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message={`已选中 ${selectedRowKeys.length} 个资源`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
              请选择要设置的状态：
            </label>
            <Select
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: '100%' }}
              options={getSelectOptions('Common.Status')}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </div>
        </div>
      </Modal>
    </>
  );
}

export default ResourceManagementPage;
