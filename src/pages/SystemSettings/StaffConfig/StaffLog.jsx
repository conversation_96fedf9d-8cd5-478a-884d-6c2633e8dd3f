import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  Input,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Popconfirm,
} from 'antd';
import {
  DeleteOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchManageLogs,
  deleteManageLog,
  batchDeleteManageLogs,
  addManageLog,
  updateManageLog,
  setFilters,
  setPagination,
} from '@/redux/staffLogPage/staffLogPageSlice';
import dayjs from 'dayjs';
import PageTabs from '@/components/PageTabs/PageTabs';
import AddStaffLogComponent from './AddStaffLogComponent';
import { usePageTabs } from '@/hooks/usePageTabs';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

function StaffLogPage() {
  const dispatch = useDispatch();
  const { logList, loading, pagination, filters } = useSelector((state) => state.staffLog);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const {
    activeTab, editingRecord, tabPanes,
    handleAdd, handleEdit, handleTabChange, handleTabEdit,
    handleSaveSuccess, handleCancel, isListTab,
    saveTabFormData, getTabFormData, clearTabFormData,
  } = usePageTabs({
    listTabLabel: '员工日志管理',
    tabTypes: {
      add: {
        label: '添加员工日志',
        prefix: 'add'
      },
      edit: {
        label: '编辑',
        prefix: 'edit',
        getLabelFn: (record) => `编辑日志 - ID: ${record.id}`
      }
    },
    dataList: logList,
    onSaveSuccess: () => {
      fetchStaffLogs();
    },
  });

  // 首次加载时获取数据
  useEffect(() => {
    fetchStaffLogs();
  }, []);

  // 获取员工日志数据
  const fetchStaffLogs = (params = {}) => {
    // 过滤掉空值和默认值，只保留有效的筛选条件
    const filteredParams = {};

    // 合并filters和params
    const allParams = { ...filters, ...params };

    // 只添加非空且非默认值的参数
    Object.keys(allParams).forEach(key => {
      const value = allParams[key];
      if (value !== null && value !== undefined && value !== "" &&
        value !== "全部方法" && value !== "全部状态码") {
        filteredParams[key] = value;
      }
    });

    const queryParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filteredParams,
    };

    dispatch(fetchManageLogs(queryParams));
  };

  // 获取方法标签颜色
  const getMethodColor = (method) => {
    switch (method) {
      case "GET":
        return "blue";
      case "POST":
        return "green";
      case "PUT":
        return "orange";
      case "DELETE":
        return "red";
      default:
        return "default";
    }
  };

  // 获取状态码标签颜色
  const getStatusColor = (status) => {
    return status >= 200 && status < 300 ? "green" : "red";
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工",
      dataIndex: "员工",
      key: "员工",
      width: 100,
    },
    {
      title: "IP地址",
      dataIndex: "IP地址",
      key: "IP地址",
      width: 150,
    },
    {
      title: "方法",
      dataIndex: "方法",
      key: "方法",
      width: 100,
      render: (method) => (
        <Tag color={getMethodColor(method)}>{method}</Tag>
      ),
    },
    {
      title: "模块",
      dataIndex: "模块",
      key: "模块",
      width: 100,
    },
    {
      title: "操作",
      dataIndex: "操作",
      key: "操作",
      width: 100,
    },
    {
      title: "状态码",
      dataIndex: "状态码",
      key: "状态码",
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "创建时间",
      key: "创建时间",
      width: 180,
      sorter: (a, b) => new Date(a.创建时间) - new Date(b.创建时间),
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此日志吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 执行批量删除
  const confirmBatchDelete = () => {
    dispatch(batchDeleteManageLogs(selectedRowKeys))
      .unwrap()
      .then(() => {
        message.success(`已删除 ${selectedRowKeys.length} 个员工日志`);
        setSelectedRowKeys([]);
        fetchStaffLogs();
      })
      .catch((error) => {
        message.error(`批量删除失败: ${error.message}`);
      });
  };

  // 处理删除
  const handleDelete = (record) => {
    dispatch(deleteManageLog(record.id))
      .unwrap()
      .then(() => {
        message.success(`已删除员工日志 ID: ${record.id}`);
        fetchStaffLogs();
      })
      .catch((error) => {
        message.error(`删除失败: ${error.message}`);
      });
  };

  // 处理查询
  const handleSearch = () => {
    fetchStaffLogs({ page: 1 });
  };

  // 重置筛选条件
  const handleReset = () => {
    dispatch(setFilters({
      manageId: "",
      ip: "",
      method: "",
      status: "",
      module: "",
      action: "",
      startDate: null,
      endDate: null
    }));
    dispatch(setPagination({ current: 1 }));
    fetchStaffLogs({
      page: 1
    });
    message.success("筛选条件已重置");
  };

  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    dispatch(setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
    }));

    fetchStaffLogs({
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortField: sorter.field,
      sortOrder: sorter.order,
    });
  };

  // 处理日期范围选择
  const handleDateRangeChange = (dates) => {
    if (!dates || dates.length === 0) {
      dispatch(setFilters({
        startDate: null,
        endDate: null,
      }));
    } else {
      dispatch(setFilters({
        startDate: dates[0].unix(),
        endDate: dates[1].unix(),
      }));
    }
  };

  // 转换日志数据
  const transformData = (data) => {
    if (!data) return [];
    return data.map(item => ({
      ...item,
      key: item.id,
      员工: `员工 ${item.manageId}`,
      IP地址: item.ip,
      方法: item.method,
      模块: item.module,
      操作: item.action,
      状态码: item.code,
      创建时间: dayjs.unix(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
    }));
  };

  return (
    <Card style={{ backgroundColor: '#fff' }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 搜索表单 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col>
                <Input
                  placeholder="搜索员工ID"
                  value={filters.manageId}
                  onChange={(e) => dispatch(setFilters({ manageId: e.target.value }))}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Input
                  placeholder="搜索IP地址"
                  value={filters.ip}
                  onChange={(e) => dispatch(setFilters({ ip: e.target.value }))}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Select
                  placeholder="选择请求方法"
                  value={filters.method || undefined}
                  onChange={(value) => dispatch(setFilters({ method: value || "" }))}
                  allowClear
                  style={{ width: 120 }}
                >
                  <Option value="GET">GET</Option>
                  <Option value="POST">POST</Option>
                  <Option value="PUT">PUT</Option>
                  <Option value="DELETE">DELETE</Option>
                </Select>
              </Col>
              <Col>
                <Input
                  placeholder="搜索模块"
                  value={filters.module}
                  onChange={(e) => dispatch(setFilters({ module: e.target.value }))}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Input
                  placeholder="搜索操作"
                  value={filters.action}
                  onChange={(e) => dispatch(setFilters({ action: e.target.value }))}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <span>时间范围</span>
              </Col>
              <Col>
                <RangePicker
                  style={{ width: 240 }}
                  value={filters.startDate && filters.endDate ? [
                    dayjs.unix(filters.startDate),
                    dayjs.unix(filters.endDate)
                  ] : null}
                  onChange={handleDateRangeChange}
                />
              </Col>
              <Col>
                <Button
                  type="primary"
                  onClick={handleSearch}
                >
                  查询
                </Button>
              </Col>
              <Col>
                <Button onClick={handleReset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                type="primary"
                onClick={handleAdd}
              >
                添加员工日志
              </Button>
              <Popconfirm
                title="确定要批量删除选中的日志吗？"
                onConfirm={confirmBatchDelete}
                okText="确定"
                cancelText="取消"
                disabled={selectedRowKeys.length === 0}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
              {selectedRowKeys.length > 0 && (
                <span style={{ marginLeft: 8 }}>
                  已选择 <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a>{" "}
                  项
                </span>
              )}
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              rowSelection={rowSelection}
              columns={columns}
              dataSource={transformData(logList)}
              loading={loading}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              onChange={handleTableChange}
              scroll={{ x: 1200 }}
            />
          </Card>
        </>
      ) : (
        /* 添加/编辑员工日志组件 */
        <AddStaffLogComponent
          editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}
    </Card>
  );
}

export default StaffLogPage;
