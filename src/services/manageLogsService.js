import api from '@/api/axios';

// 员工日志服务
const ManageLogsService = {
    // 获取员工日志列表
    listManageLogs: (params) => {
        return api.get('/manage/logs/list', { params });
    },

    // 获取单个员工日志
    getManageLog: (id) => {
        return api.get(`/manage/logs/${id}`);
    },

    // 添加员工日志
    addManageLog: (logData) => {
        return api.post('/manage/logs', logData);
    },

    // 更新员工日志
    updateManageLog: (id, logData) => {
        return api.put(`/manage/logs/${id}`, logData);
    },

    // 删除员工日志
    deleteManageLog: (id) => {
        return api.delete(`/manage/logs/${id}`);
    },

    // 批量删除员工日志
    batchDeleteManageLogs: (ids) => {
        return api.delete('/manage/logs/batch', { data: ids });
    }
};

export default ManageLogsService; 