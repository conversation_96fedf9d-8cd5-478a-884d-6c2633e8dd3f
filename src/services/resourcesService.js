import api from '@/api/axios';

// 资源管理服务
const ResourcesService = {
    // 获取资源列表
    listResources: (params) => {
        return api.get('/resources/list', { params });
    },

    // 获取单个资源
    getResource: (id) => {
        return api.get(`/resources/${id}`);
    },

    // 添加资源
    addResource: (resourceData) => {
        return api.put('/resources', resourceData);
    },

    // 更新资源
    updateResource: (id, resourceData) => {
        return api.put(`/resources/${id}`, resourceData);
    },

    // 删除资源
    deleteResource: (id) => {
        return api.delete(`/resources/${id}`);
    },

    // 批量删除资源
    batchDeleteResources: (ids) => {
        return api.delete('/resources/batch', { params: { ids } });
    },

    // 批量更新资源状态
    batchUpdateResourcesStatus: (data) => {
        return api.post('/resources/batch/status', data);
    }
};

export default ResourcesService; 