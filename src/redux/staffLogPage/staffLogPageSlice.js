import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import ManageLogsService from '@/services/manageLogsService';
import dayjs from 'dayjs';

// 初始状态
const initialState = {
    logList: [],
    currentLog: null,
    loading: false,
    error: null,
    pagination: {
        current: 1,
        pageSize: 20,
        total: 0
    },
    filters: {
        manageId: "",
        ip: "",
        method: "",
        status: "",
        module: "",
        action: "",
        startDate: null,
        endDate: null
    }
};

// 异步Thunk - 获取员工日志列表
export const fetchManageLogs = createAsyncThunk(
    'staffLog/fetchManageLogs',
    async (params, { rejectWithValue }) => {
        try {
            const response = await ManageLogsService.listManageLogs(params);
            return response;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 获取单个员工日志
export const fetchManageLogById = createAsyncThunk(
    'staffLog/fetchManageLogById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await ManageLogsService.getManageLog(id);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 添加员工日志
export const addManageLog = createAsyncThunk(
    'staffLog/addManageLog',
    async (logData, { rejectWithValue }) => {
        try {
            const response = await ManageLogsService.addManageLog(logData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 更新员工日志
export const updateManageLog = createAsyncThunk(
    'staffLog/updateManageLog',
    async ({ id, logData }, { rejectWithValue }) => {
        try {
            await ManageLogsService.updateManageLog(id, logData);
            return { id, ...logData };
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 删除员工日志
export const deleteManageLog = createAsyncThunk(
    'staffLog/deleteManageLog',
    async (id, { rejectWithValue }) => {
        try {
            await ManageLogsService.deleteManageLog(id);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 批量删除员工日志
export const batchDeleteManageLogs = createAsyncThunk(
    'staffLog/batchDeleteManageLogs',
    async (ids, { rejectWithValue }) => {
        try {
            await ManageLogsService.batchDeleteManageLogs(ids);
            return ids;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 创建切片
const staffLogSlice = createSlice({
    name: 'staffLog',
    initialState,
    reducers: {
        setFilters: (state, action) => {
            state.filters = { ...state.filters, ...action.payload };
        },
        setPagination: (state, action) => {
            state.pagination = { ...state.pagination, ...action.payload };
        },
        resetStaffLogState: () => initialState
    },
    extraReducers: (builder) => {
        builder
            // 获取员工日志列表
            .addCase(fetchManageLogs.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchManageLogs.fulfilled, (state, action) => {
                state.loading = false;

                // 确保data.data是数组
                if (action.payload && action.payload.data) {
                    const { data } = action.payload;

                    // 处理分页信息
                    state.pagination.total = data.total || 0;
                    state.pagination.current = data.curPage || 1;
                    state.pagination.maxPage = data.maxPage || 1;

                    // 处理数据列表
                    if (Array.isArray(data.data)) {
                        state.logList = data.data.map(item => ({
                            ...item,
                            key: item.id.toString(),
                            员工: `Admin ${String.fromCharCode(65 + (item.manageId % 5))}`, // 模拟员工名称，实际应从后端获取
                            IP地址: item.ip,
                            方法: item.method,
                            模块: item.module,
                            操作: item.action,
                            状态码: item.code,
                            创建时间: dayjs(item.createTime * 1000).format('YYYY-MM-DD HH:mm:ss')
                        }));
                    } else if (data.data && typeof data.data === 'object') {
                        // 如果data.data是单个对象，将其转换为数组
                        const item = data.data;
                        state.logList = [{
                            ...item,
                            key: item.id.toString(),
                            员工: `Admin ${String.fromCharCode(65 + (item.manageId % 5))}`, // 模拟员工名称，实际应从后端获取
                            IP地址: item.ip,
                            方法: item.method,
                            模块: item.module,
                            操作: item.action,
                            状态码: item.code,
                            创建时间: dayjs(item.createTime * 1000).format('YYYY-MM-DD HH:mm:ss')
                        }];
                    } else {
                        state.logList = [];
                    }
                } else {
                    state.logList = [];
                }
            })
            .addCase(fetchManageLogs.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '获取员工日志列表失败' };
            })

            // 获取单个员工日志
            .addCase(fetchManageLogById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchManageLogById.fulfilled, (state, action) => {
                state.loading = false;
                state.currentLog = action.payload;
            })
            .addCase(fetchManageLogById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '获取员工日志详情失败' };
            })

            // 添加员工日志
            .addCase(addManageLog.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(addManageLog.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(addManageLog.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '添加员工日志失败' };
            })

            // 更新员工日志
            .addCase(updateManageLog.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateManageLog.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updateManageLog.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '更新员工日志失败' };
            })

            // 删除员工日志
            .addCase(deleteManageLog.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteManageLog.fulfilled, (state, action) => {
                state.loading = false;
                state.logList = state.logList.filter(log => log.id !== action.payload);
            })
            .addCase(deleteManageLog.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '删除员工日志失败' };
            })

            // 批量删除员工日志
            .addCase(batchDeleteManageLogs.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(batchDeleteManageLogs.fulfilled, (state, action) => {
                state.loading = false;
                state.logList = state.logList.filter(log => !action.payload.includes(log.id));
            })
            .addCase(batchDeleteManageLogs.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '批量删除员工日志失败' };
            });
    }
});

// 导出 actions
export const { setFilters, setPagination, resetStaffLogState } = staffLogSlice.actions;

// 导出 reducer
export default staffLogSlice.reducer; 