import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import ResourcesService from '@/services/resourcesService';
import dayjs from 'dayjs';

// 初始状态
const initialState = {
    resourceList: [],
    currentResource: null,
    loading: false,
    error: null,
    pagination: {
        current: 1,
        pageSize: 20,
        total: 0
    },
    filters: {
        status: "全部状态",
        level: "全部层级",
        menuName: "",
        dateRange: null
    }
};

// 异步Thunk - 获取资源列表
export const fetchResources = createAsyncThunk(
    'resourceManagement/fetchResources',
    async (params, { rejectWithValue }) => {
        try {
            const response = await ResourcesService.listResources(params);
            return response;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 获取单个资源
export const fetchResourceById = createAsyncThunk(
    'resourceManagement/fetchResourceById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await ResourcesService.getResource(id);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 添加资源
export const addResource = createAsyncThunk(
    'resourceManagement/addResource',
    async (resourceData, { rejectWithValue }) => {
        try {
            const response = await ResourcesService.addResource(resourceData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 更新资源
export const updateResource = createAsyncThunk(
    'resourceManagement/updateResource',
    async ({ id, resourceData }, { rejectWithValue }) => {
        try {
            await ResourcesService.updateResource(id, resourceData);
            return { id, ...resourceData };
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 删除资源
export const deleteResource = createAsyncThunk(
    'resourceManagement/deleteResource',
    async (id, { rejectWithValue }) => {
        try {
            await ResourcesService.deleteResource(id);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 批量删除资源
export const batchDeleteResources = createAsyncThunk(
    'resourceManagement/batchDeleteResources',
    async (ids, { rejectWithValue }) => {
        try {
            await ResourcesService.batchDeleteResources(ids);
            return ids;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 异步Thunk - 批量更新资源状态
export const batchUpdateResourcesStatus = createAsyncThunk(
    'resourceManagement/batchUpdateResourcesStatus',
    async ({ ids, status }, { rejectWithValue }) => {
        try {
            await ResourcesService.batchUpdateResourcesStatus({ ids, status });
            return { ids, status };
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

// 创建切片
const resourceManagementSlice = createSlice({
    name: 'resourceManagement',
    initialState,
    reducers: {
        setFilters: (state, action) => {
            state.filters = { ...state.filters, ...action.payload };
        },
        setPagination: (state, action) => {
            state.pagination = { ...state.pagination, ...action.payload };
        },
        resetResourceManagementState: () => initialState
    },
    extraReducers: (builder) => {
        builder
            // 获取资源列表
            .addCase(fetchResources.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchResources.fulfilled, (state, action) => {
                state.loading = false;

                // 确保data.data是数组
                if (action.payload && action.payload.data) {
                    const { data } = action.payload;

                    // 处理分页信息
                    state.pagination.total = data.total || 0;
                    state.pagination.current = data.curPage || 1;
                    state.pagination.maxPage = data.maxPage || 1;

                    // 处理数据列表
                    if (Array.isArray(data.data)) {
                        state.resourceList = data.data.map(item => ({
                            ...item,
                            key: item.id.toString(),
                            父名称: item.parentMenuName,
                            层级: item.level === "1" ? "菜单" :
                                item.level === "2" ? "模块" :
                                    item.level === "3" ? "页面" : "标签",
                            status: item.status, // 保留原始状态值用于枚举显示
                            菜单名称: item.menuName,
                            菜单路径: item.menuPath,
                            资源键: item.resourcesKey,
                            创建时间: dayjs(item.createTime * 1000).format('YYYY-MM-DD HH:mm:ss')
                        }));
                    } else if (data.data && typeof data.data === 'object') {
                        // 如果data.data是单个对象，将其转换为数组
                        const item = data.data;
                        state.resourceList = [{
                            ...item,
                            key: item.id.toString(),
                            父名称: item.parentMenuName,
                            层级: item.level === "1" ? "菜单" :
                                item.level === "2" ? "模块" :
                                    item.level === "3" ? "页面" : "标签",
                            status: item.status, // 保留原始状态值用于枚举显示
                            菜单名称: item.menuName,
                            菜单路径: item.menuPath,
                            资源键: item.resourcesKey,
                            创建时间: dayjs(item.createTime * 1000).format('YYYY-MM-DD HH:mm:ss')
                        }];
                    } else {
                        state.resourceList = [];
                    }
                } else {
                    state.resourceList = [];
                }
            })
            .addCase(fetchResources.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '获取资源列表失败' };
            })

            // 获取单个资源
            .addCase(fetchResourceById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchResourceById.fulfilled, (state, action) => {
                state.loading = false;
                state.currentResource = action.payload;
            })
            .addCase(fetchResourceById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '获取资源详情失败' };
            })

            // 添加资源
            .addCase(addResource.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(addResource.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(addResource.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '添加资源失败' };
            })

            // 更新资源
            .addCase(updateResource.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateResource.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updateResource.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '更新资源失败' };
            })

            // 删除资源
            .addCase(deleteResource.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteResource.fulfilled, (state, action) => {
                state.loading = false;
                state.resourceList = state.resourceList.filter(resource => resource.id !== action.payload);
            })
            .addCase(deleteResource.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '删除资源失败' };
            })

            // 批量删除资源
            .addCase(batchDeleteResources.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(batchDeleteResources.fulfilled, (state, action) => {
                state.loading = false;
                state.resourceList = state.resourceList.filter(resource => !action.payload.includes(resource.id));
            })
            .addCase(batchDeleteResources.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '批量删除资源失败' };
            })

            // 批量更新资源状态
            .addCase(batchUpdateResourcesStatus.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(batchUpdateResourcesStatus.fulfilled, (state, action) => {
                state.loading = false;
                const { ids, status } = action.payload;
                state.resourceList = state.resourceList.map(resource => {
                    if (ids.includes(resource.id)) {
                        return {
                            ...resource,
                            status
                        };
                    }
                    return resource;
                });
            })
            .addCase(batchUpdateResourcesStatus.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || { message: '批量更新资源状态失败' };
            });
    }
});

// 导出 actions
export const { setFilters, setPagination, resetResourceManagementState } = resourceManagementSlice.actions;

// 导出 reducer
export default resourceManagementSlice.reducer; 